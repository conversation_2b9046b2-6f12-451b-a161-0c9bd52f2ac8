<div class="container pt-3 pb-5 mb-5">
  <p-toast position="top-right" styleClass="custom-toast"></p-toast>

  <!-- Loading Overlay for form submission -->
  <div *ngIf="isLoading" class="loading-overlay">
    <div class="loading-content">
      <p-progressSpinner
        strokeWidth="4"
        [style]="{ width: '50px', height: '50px' }"
      ></p-progressSpinner>
      <div class="mt-3 text-white">Processing your request...</div>
    </div>
  </div>

  <form [formGroup]="userForm" (ngSubmit)="onSubmit()">
    <div
      class="d-flex align-items-center justify-content-between header-container"
    >
      <div class="d-flex align-items-center">
        <a (click)="navigateBack()" class="back-button">
          <i class="pi pi-arrow-left"></i>
          <h2 class="mb-0">{{ isEditMode ? "Edit User" : "Add User" }}</h2>
        </a>
      </div>
      <div class="action-buttons">
        <button
          pButton
          type="button"
          label="Cancel"
          class="p-button-outlined p-button-danger me-2 cancel-btn"
          (click)="navigateBack()"
        ></button>
        <button
          pButton
          type="submit"
          label="{{ isLoading ? 'Saving...' : 'Save' }}"
          class="p-button-danger save-btn"
          [disabled]="isLoading"
        ></button>
      </div>
    </div>

    <!-- Error summary section -->
    <!-- <div
      class="alert alert-danger rounded-4 mb-3"
      *ngIf="validationErrors.length > 0"
    >
      <h6 class="alert-heading mb-2">Please fix the following errors:</h6>
      <ul class="mb-0 ps-3">
        <li *ngFor="let error of validationErrors">{{ error }}</li>
      </ul>
    </div>

    <div class="alert alert-light rounded-4 mb-3" *ngIf="successMessage">
      {{ successMessage }}
    </div> -->
    <div class="card mb-4 p-4">
      <h5 class="mb-4">Profile</h5>
      <div class="row">
        <div class="col-12 col-md-6 mb-4">
          <label for="name" class="form-label mb-2"
            >Name<span class="text-danger">*</span></label
          >
          <input
            pInputText
            id="name"
            type="text"
            class="w-100"
            placeholder="Name"
            formControlName="FullName"
            [style]="{ height: '39px' }"
            [ngClass]="{
              'ng-invalid ng-dirty':
                (formSubmitted || userForm.get('FullName')?.touched) &&
                userForm.get('FullName')?.invalid,
            }"
          />
          <div
            *ngIf="
              (formSubmitted || userForm.get('FullName')?.touched) &&
              userForm.get('FullName')?.invalid
            "
            class="text-danger small mt-1"
          >
            Name is required
          </div>
        </div>

        <div class="col-12 col-md-6 mb-4">
          <label for="email" class="form-label mb-2"
            >Email<span class="text-danger">*</span></label
          >
          <input
            pInputText
            id="email"
            type="email"
            class="w-100"
            placeholder="Email"
            formControlName="email"
            pattern="^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
            [readonly]="isEditMode"
            [style]="{ height: '39px' }"
            [ngClass]="{
              'ng-invalid ng-dirty':
                (formSubmitted || userForm.get('email')?.touched) &&
                userForm.get('email')?.invalid,
              'p-disabled': isEditMode,
            }"
          />
          <div
            *ngIf="
              (formSubmitted || userForm.get('email')?.touched) &&
              userForm.get('email')?.errors?.['required']
            "
            class="text-danger small mt-1"
          >
            Email is required
          </div>
          <div
            *ngIf="
              (formSubmitted || userForm.get('email')?.touched) &&
              userForm.get('email')?.errors?.['pattern']
            "
            class="text-danger small mt-1"
          >
            Please enter a valid email address
          </div>
        </div>

        <div class="col-12 col-md-6 mb-4">
          <label for="phoneNumber" class="form-label mb-2"
            >Phone Number<span class="text-danger">*</span></label
          >
          <input
            pInputText
            id="phoneNumber"
            type="tel"
            class="w-100"
            placeholder="10-digit phone number"
            formControlName="phoneNumber"
            pattern="^[0-9]{10}$"
            [ngClass]="{
              'ng-invalid ng-dirty':
                (formSubmitted || userForm.get('phoneNumber')?.touched) &&
                userForm.get('phoneNumber')?.invalid,
            }"
          />
          <div
            *ngIf="
              (formSubmitted || userForm.get('phoneNumber')?.touched) &&
              userForm.get('phoneNumber')?.errors?.['required']
            "
            class="text-danger small mt-1"
          >
            Phone Number is required
          </div>
          <div
            *ngIf="
              (formSubmitted || userForm.get('phoneNumber')?.touched) &&
              userForm.get('phoneNumber')?.errors?.['pattern']
            "
            class="text-danger small mt-1"
          >
            Phone Number must be 10 digits
          </div>
        </div>

        <div class="col-12 col-md-6 mb-4">
          <label for="website" class="form-label mb-2">Website</label>
          <input
            pInputText
            id="website"
            type="url"
            class="w-100"
            placeholder="Link"
            formControlName="website"
            pattern="(https?:\/\/)?(www\.)?[a-zA-Z0-9-]+(\.[a-zA-Z]{2,})+\/?"
            [ngClass]="{
              'ng-invalid ng-dirty':
                (formSubmitted || userForm.get('website')?.touched) &&
                userForm.get('website')?.invalid,
            }"
          />
          <div
            *ngIf="
              (formSubmitted || userForm.get('website')?.touched) &&
              userForm.get('website')?.errors?.['required']
            "
            class="text-danger small mt-1"
          >
            Website is required
          </div>
          <div
            *ngIf="
              (formSubmitted || userForm.get('website')?.touched) &&
              userForm.get('website')?.errors?.['pattern']
            "
            class="text-danger small mt-1"
          >
            Please enter a valid URL
          </div>
        </div>

        <div class="col-12 col-md-6 mb-4">
          <label for="role" class="form-label mb-2"
            >Role<span class="text-danger">*</span></label
          >
          <p-dropdown
            id="role"
            [options]="[
              { label: 'Global Admin', value: 'Global Admin' },
              { label: 'Department Admin', value: 'Department Admin' },
              { label: 'Event Organizer', value: 'Event Organizer' },
            ]"
            formControlName="role"
            placeholder="Select"
            [showClear]="false"
            styleClass="w-100 dropdown-field"
            [style]="{ height: '39px' }"
            [ngClass]="{
              'ng-invalid ng-dirty':
                (formSubmitted || userForm.get('role')?.touched) &&
                userForm.get('role')?.invalid,
            }"
          ></p-dropdown>
          <div
            *ngIf="
              (formSubmitted || userForm.get('role')?.touched) &&
              userForm.get('role')?.errors?.['required']
            "
            class="text-danger small mt-1"
          >
            Role is required
          </div>
        </div>
      </div>

      <div class="row">
        <div class="col-12 mb-4">
          <label for="description" class="form-label mb-2">Description</label>
          <textarea
            pTextarea
            id="description"
            rows="4"
            placeholder="Description"
            formControlName="description"
            class="w-100"
            autoResize="true"
            [ngClass]="{
              'ng-invalid ng-dirty':
                (formSubmitted || userForm.get('description')?.touched) &&
                userForm.get('description')?.invalid,
            }"
          ></textarea>
          <div
            *ngIf="
              (formSubmitted || userForm.get('description')?.touched) &&
              userForm.get('description')?.errors?.['required']
            "
            class="text-danger small mt-1"
          >
            Description is required
          </div>
        </div>
      </div>
    </div>

    <div class="card mb-4 p-4">
      <h5 class="mb-4">Social Media and Marketing</h5>
      <div class="row">
        <div class="col-12 col-md-6 mb-4">
          <label for="facebookId" class="form-label mb-2">Facebook</label>
          <div class="position-relative">
            <div
              class="position-absolute"
              style="
                top: 50%;
                transform: translateY(-50%);
                left: 12px;
                z-index: 10;
                pointer-events: none;
              "
            >
              <i class="pi pi-facebook" style="color: #4267b2"></i>
            </div>
            <input
              pInputText
              id="facebookId"
              type="text"
              placeholder="www.facebook.com/123"
              formControlName="facebook"
              pattern="(https?:\/\/)?(www\.)?facebook\.com\/[a-zA-Z0-9\.]+"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  (formSubmitted || userForm.get('facebook')?.touched) &&
                  userForm.get('facebook')?.invalid,
              }"
              style="padding-left: 40px; width: 100%; height: 39px"
            />
          </div>
          <div
            *ngIf="
              (formSubmitted || userForm.get('facebook')?.touched) &&
              userForm.get('facebook')?.errors?.['required']
            "
            class="text-danger small mt-1"
          >
            Facebook is required
          </div>
          <div
            *ngIf="
              (formSubmitted || userForm.get('facebook')?.touched) &&
              userForm.get('facebook')?.errors?.['pattern']
            "
            class="text-danger small mt-1"
          >
            Please enter a valid Facebook URL
          </div>
        </div>

        <div class="col-12 col-md-6 mb-4">
          <label for="twitterId" class="form-label mb-2">Twitter (X)</label>
          <div class="position-relative">
            <div
              class="position-absolute"
              style="
                top: 50%;
                transform: translateY(-50%);
                left: 12px;
                z-index: 10;
                pointer-events: none;
              "
            >
              <i class="pi pi-twitter" style="color: #1da1f2"></i>
            </div>
            <input
              pInputText
              id="twitterId"
              type="text"
              class="w-100"
              placeholder="www.twitter.com/username"
              formControlName="twitter"
              pattern="(https?:\/\/)?(www\.)?(twitter\.com|x\.com)\/[A-Za-z0-9_]{1,15}"
              [ngClass]="{
                'ng-invalid ng-dirty':
                  (formSubmitted || userForm.get('twitter')?.touched) &&
                  userForm.get('twitter')?.invalid,
              }"
              style="padding-left: 40px; width: 100%; height: 39px"
            />
          </div>
          <div
            *ngIf="
              (formSubmitted || userForm.get('twitter')?.touched) &&
              userForm.get('twitter')?.errors?.['required']
            "
            class="text-danger small mt-1"
          >
            Twitter is required
          </div>
          <div
            *ngIf="
              (formSubmitted || userForm.get('twitter')?.touched) &&
              userForm.get('twitter')?.errors?.['pattern']
            "
            class="text-danger small mt-1"
          >
            Please enter a valid Twitter/X URL
          </div>
        </div>
      </div>
    </div>

    <!-- Fixed button container for mobile -->
    <div class="fixed-bottom-buttons d-md-none">
      <div class="container py-2 bg-white border-top">
        <div class="d-flex justify-content-end">
          <button
            pButton
            type="button"
            label="Cancel"
            class="p-button-outlined p-button-danger me-2"
            (click)="navigateBack()"
          ></button>
          <button
            pButton
            type="submit"
            label="{{ isLoading ? 'Saving...' : 'Save' }}"
            class="p-button-danger save-btn"
            [disabled]="isLoading"
          ></button>
        </div>
      </div>
    </div>
  </form>
</div>
