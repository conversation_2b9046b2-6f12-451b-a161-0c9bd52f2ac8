import { Component, inject, OnInit } from '@angular/core';
import { ResourcesService } from '../../../Core/Services/resources.service';
import { MessageService, ConfirmationService } from 'primeng/api';
import { User } from '../../../Core/Models/User';
import { Resource } from '../../../Core/Models/resources';
import { Router } from '@angular/router';
import { FormBuilder, FormGroup } from '@angular/forms';
import { debounceTime, finalize } from 'rxjs/operators';
import { HttpErrorResponse } from '@angular/common/http';
import { throwError } from 'rxjs';
import { environment } from '../../../../environments/environment';

@Component({
  selector: 'app-resource-list',
  standalone: false,
  templateUrl: './resource-list.component.html',
  styleUrl: './resource-list.component.scss',
  providers: [MessageService, ConfirmationService],
})
export class ResourceListComponent implements OnInit {
  Math = Math;
  resources: Resource[] = [];
  error: string | null = null;
  isLoading = false;
  router = inject(Router);
  showFilters = false;
  filterForm!: FormGroup;
  private apiUrl = environment.apiUrl;

  // Pagination
  currentPage = 1;
  pageSize = 10;
  totalItems = 0;
  totalPages = 0;

  // Sorting
  currentSortField?: string;
  currentSortOrder: 'asc' | 'desc' = 'asc';

  // Utility method to get full image path
  getFullImagePath(relativePath: string): string {
    if (!relativePath) return 'assets/images/placeholder.jpg';

    // If it's already a full URL (from S3), return it as is
    if (relativePath.startsWith('http')) {
      return relativePath;
    }

    // Otherwise, use the Files controller
    return `${this.apiUrl}/api/Files${relativePath.replace(/^\/+/, '/')}`;
  }

  constructor(
    private resourcesService: ResourcesService,
    private fb: FormBuilder,
    private messageService: MessageService,
    private confirmationService: ConfirmationService,
  ) {
    this.filterForm = this.fb.group({
      organizationalTitle: [''],
      resourceType: [''],
      category: [''],
    });

    this.filterForm.valueChanges.pipe(debounceTime(300)).subscribe(() => {
      this.currentPage = 1; // Reset to first page when filters change
      this.loadResources();
    });
  }
  ngOnInit(): void {
    this.loadResources();
  }

  loadResources(): void {
    this.error = null;
    this.isLoading = true;

    const params = {
      pageNumber: this.currentPage,
      pageSize: this.pageSize,
      sortField: this.currentSortField,
      sortOrder: this.currentSortOrder,
      filters: this.filterForm.value,
    };

    this.resourcesService
      .getResources(params)
      .pipe(finalize(() => (this.isLoading = false)))
      .subscribe({
        next: (response) => {
          this.resources = response.items;
          this.totalItems = response.totalItems;
          this.totalPages = response.totalPages;
        },
        error: (err) => {
          this.error = err.message;
          this.messageService.add({
            severity: 'error',
            summary: 'Error',
            detail:
              err.message || 'Failed to load resources. Please try again.',
          });
        },
      });
  }

  toggleFilters(): void {
    this.showFilters = !this.showFilters;
  }

  clearFilters(): void {
    this.filterForm.reset();
    this.currentPage = 1;
    this.loadResources();
  }

  onPageChange(page: number): void {
    if (page >= 1 && page <= this.totalPages && page !== this.currentPage) {
      this.currentPage = page;
      this.loadResources();
    }
  }

  sortResources(field: string): void {
    if (this.currentSortField === field) {
      this.currentSortOrder = this.currentSortOrder === 'asc' ? 'desc' : 'asc';
    } else {
      this.currentSortField = field;
      this.currentSortOrder = 'asc';
    }
    this.loadResources();
  }

  getSortIcon(field: string): string {
    if (this.currentSortField !== field) {
      return 'bi-chevron-expand';
    }
    return this.currentSortOrder === 'asc'
      ? 'bi-chevron-up'
      : 'bi-chevron-down';
  }

  get pages(): number[] {
    if (this.totalPages <= 5) {
      return Array.from({ length: this.totalPages }, (_, i) => i + 1);
    }

    if (this.currentPage <= 3) {
      return [1, 2, 3, 4, 5];
    }

    if (this.currentPage >= this.totalPages - 2) {
      return Array.from({ length: 5 }, (_, i) => this.totalPages - 4 + i);
    }

    return Array.from({ length: 5 }, (_, i) => this.currentPage - 2 + i);
  }

  viewResourceDetails(resourceId: number): void {
    this.router.navigate(['/resources', resourceId]);
  }

  addResource(): void {
    this.router.navigate(['/resources/new']);
  }

  editResource(resourceId: number): void {
    this.router.navigate(['/resources', resourceId, 'edit']);
  }

  deleteResource(resourceId: number): void {
    // Find the resource name for better UX
    const resource = this.resources.find((r) => r.id === resourceId);
    const resourceName = resource
      ? resource.organizationTitle
      : 'this resource';

    this.confirmationService.confirm({
      message: `Are you sure you want to delete?`,
      header: 'Delete Resource',
      icon: 'pi pi-exclamation-triangle',
      acceptLabel: 'Yes, Delete',
      rejectLabel: 'Cancel',
      acceptButtonStyleClass: 'p-button-danger',
      rejectButtonStyleClass: 'p-button-text',
      defaultFocus: 'reject',
      key: 'deleteResource',
      accept: () => {
        this.resourcesService.DeleteResource(resourceId).subscribe({
          next: () => {
            this.messageService.add({
              severity: 'success',
              summary: 'Success',
              detail: `Resource "${resourceName}" deleted successfully.`,
            });
            this.loadResources();
          },
          error: (err) => {
            this.messageService.add({
              severity: 'error',
              summary: 'Error',
              detail:
                err.message || 'Failed to delete resource. Please try again.',
            });
          },
        });
      },
      reject: () => {
        // Optional: Add feedback for cancellation
        // this.messageService.add({
        //   severity: 'info',
        //   summary: 'Cancelled',
        //   detail: 'Delete operation cancelled.',
        //   life: 2000
        // });
      },
    });
  }

  resourceTypes: { name: string; value: string }[] = [
    { name: 'All Resource Types', value: '' },
    { name: 'External Partner', value: 'ExternalPartner' },
    {
      name: 'South Ward Promise Neighbourhood',
      value: 'SouthWardPromiseNeighbourhood',
    },
    { name: 'SWPN Partner', value: 'SWPNPartner' },
  ];

  // Add this property for resource categories
  resourceCategories: { name: string; value: string }[] = [
    { name: 'All Categories', value: '' },
    { name: 'Education', value: 'Education' },
    { name: 'Healthcare', value: 'Healthcare' },
    { name: 'Housing', value: 'Housing' },
    { name: 'Employment', value: 'Employment' },
    { name: 'Food Assistance', value: 'Food Assistance' },
    { name: 'Mental Health', value: 'Mental Health' },
    { name: 'Community Services', value: 'Community Services' },
    { name: 'Youth Programs', value: 'Youth Programs' },
    { name: 'Senior Services', value: 'Senior Services' },
    { name: 'Legal Aid', value: 'Legal Aid' },
  ];

  formatResourceType(type: string): string {
    switch (type) {
      case 'SouthWardPromiseNeighbourhood':
        return 'South Ward Promise Neighbourhood';
      case 'ExternalPartner':
        return 'External Partner';
      case 'SWPNPartner':
        return 'SWPN Partner';
      default:
        return type;
    }
  }
  formatResourceCategory(category: string): string {
    switch (category) {
      case 'Education':
        return 'Education';
      case 'Healthcare':
        return 'Health care';
      case 'Housing':
        return 'Housing';
      case 'Employment':
        return 'Employment';
      case 'Food Assistance':
        return 'Food Assistance';
      case 'Mental Health':
        return 'Mental Health';
      case 'Community Services':
        return 'Community Services';
      case 'Youth Programs':
        return 'Youth Programs';
      case 'Senior Services':
        return 'Senior Services';
      case 'Legal Aid':
        return 'Legal Aid';
      default:
        return category;
    }
  }
}
